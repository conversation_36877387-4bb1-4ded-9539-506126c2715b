import { ExchangeRateType } from "@skywind-group/sw-currency-exchange";
import type { ProviderExchangeRate } from "../entities/exchangeRates";
import type { ProviderRates } from "./provider";
import type { logging } from "@skywind-group/sw-utils";
import { getSecuredObjectData } from "@skywind-group/sw-wallet-adapter-core";

const DAY = 24 * 60 * 60 * 1000;

export function getTimestamp(date: Date): string {
    return date instanceof Date ? date.toISOString().substr(0, 10) : date;
}

export function getToday(): Date {
    const date = new Date();
    date.setUTCHours(0, 0, 0, 0);
    return date;
}

export function getPrevDay(date = new Date(), days = 1): Date {
    return adjustDate(date, -days);
}

export function getNextDay(date = new Date(), days = 1): Date {
    return adjustDate(date, days);
}

function adjustDate(date: Date, days: number): Date {
    const nextDay = new Date(date);
    nextDay.setUTCDate(nextDay.getUTCDate() + days);
    nextDay.setUTCHours(0, 0, 0, 0);
    return nextDay;
}

export function getDaysCount(startDate: Date, endDate: Date) {
    return Math.floor((endDate.getTime() - startDate.getTime()) / DAY);
}

export function mapProviderRates({ askRates, bidRates, provider, ts }: ProviderRates): ProviderExchangeRate[] {
    const result: ProviderExchangeRate[] = [];
    for (const fromCurrency of Object.keys(bidRates)) {
        for (const toCurrency of Object.keys(bidRates[fromCurrency])) {
            result.push({
                from: fromCurrency,
                to: toCurrency,
                rate: bidRates[fromCurrency][toCurrency],
                providerDate: ts,
                provider,
                type: ExchangeRateType.BID
            });
        }
    }
    for (const fromCurrency of Object.keys(askRates)) {
        for (const toCurrency of Object.keys(askRates[fromCurrency])) {
            result.push({
                from: fromCurrency,
                to: toCurrency,
                rate: askRates[fromCurrency][toCurrency],
                providerDate: ts,
                provider,
                type: ExchangeRateType.ASK
            });
        }
    }
    return result;
}

export function getRateKey(rate: ProviderExchangeRate) {
    return `${rate.from}-${rate.to}-${rate.type}`;
}

export function getSecuredUrl(url: string, secureKeys: string[] = []) {
    try {
        const request = new URL(url);
        const query = getSecuredObjectData(Object.fromEntries(request.searchParams), secureKeys);
        request.searchParams.forEach((_, key) => {
            request.searchParams.delete(key);
        });
        Object.entries(query).forEach(([key, value]) => {
            request.searchParams.set(key, value as string);
        });
        return request.href;
    } catch (err) {
        return url;
    }
}

export function logRequest(method: string, url: string, log: logging.Logger, secureKeys: string[] = []) {
    try {
        const request = new URL(getSecuredUrl(url, secureKeys));
        log.info({
            method,
            url: request.href,
            query: Object.fromEntries(request.searchParams)
        }, `${method} %s`, request.origin);
    } catch (err) {
        log.error(err, "Invalid url: %s", url);
    }
}
